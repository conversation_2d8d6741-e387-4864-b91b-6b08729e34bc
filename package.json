{"name": "upace-instructor", "main": "index.js", "version": "1.0.0", "scripts": {"dev": "expo start -c --ios", "dev:web": "expo start -c --web", "dev:android": "expo start -c --android", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start -c --web", "clean": "rm -rf .expo node_modules", "postinstall": "npx tailwindcss -i ./global.css -o ./node_modules/.cache/nativewind/global.css"}, "dependencies": {"@babel/runtime": "^7.27.6", "@hookform/resolvers": "^3.9.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-firebase/analytics": "^21.13.0", "@react-native-firebase/app": "^21.13.0", "@react-native-picker/picker": "2.9.0", "@react-navigation/drawer": "^6.7.2", "@react-navigation/native": "^6.0.2", "@rn-primitives/accordion": "^1.2.0", "@rn-primitives/avatar": "~1.1.0", "@rn-primitives/checkbox": "^1.1.0", "@rn-primitives/dialog": "^1.1.0", "@rn-primitives/label": "^1.1.0", "@rn-primitives/portal": "~1.1.0", "@rn-primitives/progress": "~1.1.0", "@rn-primitives/select": "^1.1.0", "@rn-primitives/slot": "^1.2.0", "@rn-primitives/switch": "^1.1.0", "@rn-primitives/tabs": "^1.1.0", "@rn-primitives/tooltip": "~1.1.0", "@rn-primitives/types": "^1.1.0", "@shopify/flash-list": "1.7.3", "@shopify/react-native-skia": "1.5.0", "@tanstack/query-sync-storage-persister": "^5.59.16", "@tanstack/react-query": "^5.59.16", "@tanstack/react-query-persist-client": "^5.59.16", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^4.1.0", "expo": "~52.0.39", "expo-blur": "~14.0.3", "expo-build-properties": "~0.13.3", "expo-calendar": "~14.0.6", "expo-camera": "~16.0.18", "expo-constants": "~17.0.8", "expo-dev-client": "~5.0.20", "expo-image": "~2.0.7", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-navigation-bar": "~4.0.9", "expo-router": "~4.0.21", "expo-secure-store": "~14.0.1", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.9", "i": "^0.3.7", "ky": "^1.7.5", "lodash": "^4.17.21", "lottie-react-native": "7.1.0", "lucide-react-native": "^0.378.0", "match-sorter": "^7.0.0", "nativewind": "^4.0.33", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.53.1", "react-native": "0.76.9", "react-native-autocomplete-dropdown": "^4.0.0", "react-native-gesture-handler": "~2.20.2", "react-native-keyboard-controller": "^1.14.3", "react-native-mmkv": "2.12.2", "react-native-modal-datetime-picker": "^18.0.0", "react-native-reanimated": "~3.16.1", "react-native-root-toast": "^3.6.0", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-web": "~0.19.6", "react-native-webview": "13.12.5", "tailwind-merge": "^2.2.1", "tailwindcss": "3.3.5", "tailwindcss-animate": "^1.0.7", "zustand": "^4.5.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/lodash": "^4.17.13", "@types/react": "~18.3.12", "typescript": "~5.3.3"}, "private": true}